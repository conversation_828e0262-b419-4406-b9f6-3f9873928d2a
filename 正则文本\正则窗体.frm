Option Explicit

' 窗体级变量声明
Private sCount As Long
Private TextColor As Color

' 用于存储当前正在处理的Shape对象
Private currentProcessingShape As Shape

' 用于跟踪用户是否主动选择了颜色
Private userSelectedColor As Boolean

' 窗体初始化
Private Sub UserForm_Initialize()
    Me.Caption = macroName & " " & macroVersion

    ' 初始化颜色对象，确保使用CMYK颜色模式
    Set TextColor = New Color
    TextColor.CMYKAssign 0, 100, 100, 0
    userSelectedColor = False  ' 初始化时用户没有选择颜色

    ' 设置颜色按钮的背景色显示当前颜色
    If Not TextColor Is Nothing Then
        ' 为了显示，临时转换为RGB显示在按钮上，但保持TextColor为CMYK
        Dim tempColor As New Color
        tempColor.CopyAssign TextColor
        tempColor.ConvertToRGB
        Color.BackColor = RGB(tempColor.RGBRed, tempColor.RGBGreen, tempColor.RGBBlue)
    End If

    ' 初始化查找范围下拉框
    fanwen.AddItem "当前文档"
    fanwen.AddItem "当前页面"
    fanwen.AddItem "当前选择范围"

    ' 根据当前选择状态设置默认范围
    If ActiveSelectionRange.Count > 0 Then
        fanwen.Text = "当前选择范围"
    Else
        fanwen.Text = "当前文档"
    End If

    ' 初始化字体列表 - 参考ttt.bas的实现方式
    LoadFontList

    ' 初始化字号默认值
    zihaodaxiao.Text = "24"
    zihaodaxiaoanjian.Value = 24
    zihaodaxiaoanjian.Min = 1
    zihaodaxiaoanjian.Max = 3000

    ' 初始化格式数组
    FindFormat(0) = "0"
    ' ChangeFormat数组已废弃，现在直接检查勾选框状态
End Sub

' 加载字体列表到下拉框 - 参考ttt.bas的实现方式
Private Sub LoadFontList()
    Dim nDefaultFont As Integer, nCount As Integer
    Dim strDefaultFont As String
    Dim v As Variant

    strDefaultFont = "Times New Roman"
    nDefaultFont = 0
    nCount = 0

    ' 清空现有列表
    zitisousuo.Clear

    On Error Resume Next

    ' 直接遍历FontList，过滤掉@开头的字体
    For Each v In FontList
        If Not Left(v, 1) = "@" Then
            zitisousuo.AddItem v
            If v = strDefaultFont Then nDefaultFont = nCount
            nCount = nCount + 1
        End If
    Next v

    ' 设置默认选择
    If zitisousuo.ListCount > 0 Then
        zitisousuo.ListIndex = nDefaultFont
    End If

    On Error GoTo 0
End Sub

' 颜色选择按钮点击事件
Private Sub Color_Click()
    Dim c As New Color
    Dim b As Boolean
    With c
        b = .UserAssignEx
        If b Then
            TextColor.CopyAssign c
            userSelectedColor = True  ' 标记用户已主动选择颜色

            ' 保留原始颜色模型，不强制转换为RGB
            If TextColor.Type = cdrColorRGB Then
                Color.BackColor = RGB(TextColor.RGBRed, TextColor.RGBGreen, TextColor.RGBBlue)
            Else
                ' 为了显示，临时转换为RGB显示在按钮上
                Dim tempColor As New Color
                tempColor.CopyAssign TextColor
                tempColor.ConvertToRGB
                Color.BackColor = RGB(tempColor.RGBRed, tempColor.RGBGreen, tempColor.RGBBlue)
            End If
        End If
    End With
End Sub

' 查找范围下拉框变化事件
Private Sub fanwen_Change()
    ' 可以在这里添加范围变化时的处理逻辑
End Sub

' 预览滚动条变化事件
Private Sub gundongtiao_Change()
    ' 处理预览列表的滚动
End Sub

' 替换按钮点击事件
Private Sub tihuan_Click()
    Debug.Print "=== 替换按钮点击 ==="
    Debug.Print "勾选框状态检查:"
    Debug.Print "字体勾选: " & ziti.Value & ", 字体名称: " & zitisousuo.Text
    Debug.Print "字号勾选: " & zihao.Value & ", 字号: " & zihaodaxiao.Text
    Debug.Print "颜色勾选: " & tihuanyanse.Value & ", 颜色值: " & Color.BackColor
    Debug.Print "用户是否选择颜色: " & userSelectedColor
    If Not TextColor Is Nothing Then
        Debug.Print "TextColor类型: " & TextColor.Type & ", 名称: " & TextColor.Name
    Else
        Debug.Print "TextColor为空"
    End If
    Debug.Print "粗体勾选: " & Bold.Value
    Debug.Print "斜体勾选: " & xieti.Value
    Debug.Print "下划线勾选: " & xiahuaxian.Value
    Debug.Print "上标勾选: " & shangbiao.Value
    Debug.Print "下标勾选: " & xiabiao.Value

    ' 执行替换操作
    Dim findText As String
    Dim replaceText As String
    Dim shapes As ShapeRange
    Dim replaceCount As Long

    findText = chazhaowenben.Text
    replaceText = tihuanwenben.Text

    If Len(findText) = 0 Then
        MsgBox "请输入要查找的文本", vbInformation
        Exit Sub
    End If

    ' 获取要查找的形状范围
    Set shapes = myFindShapes()
    If shapes Is Nothing Then
        MsgBox "没有找到可查找的文本对象", vbInformation
        Exit Sub
    End If

    ' 执行替换操作
    replaceCount = myReplaceText(shapes, findText, replaceText, False)

    ' 更新预览列表
    UpdatePreview

    MsgBox "替换完成！共替换了 " & replaceCount & " 个匹配项", vbInformation
End Sub

' 替换文本输入框变化事件
Private Sub tihuanwenben_Change()
    ' 不执行任何操作，不再实时更新预览
End Sub

' 查找文本输入框变化事件
Private Sub chazhaowenben_Change()
    ' 不执行任何操作，不再实时更新预览
End Sub

' 查找按钮点击事件
Private Sub chazhao_Click()
    Dim findText As String
    Dim shapes As ShapeRange
    Dim foundCount As Long

    findText = chazhaowenben.Text
    If Len(findText) = 0 Then
        MsgBox "请输入要查找的文本", vbInformation
        Exit Sub
    End If

    ' 获取要查找的形状范围
    Set shapes = myFindShapes()
    If shapes Is Nothing Then
        MsgBox "没有找到可查找的文本对象", vbInformation
        Exit Sub
    End If

    ' 执行查找并统计
     foundCount = myReplaceText(shapes, findText, "", True) ' 只查找不替换

    ' 更新预览列表
    UpdatePreview

    MsgBox "找到 " & foundCount & " 个匹配项", vbInformation
End Sub

' 预览列表点击事件
Private Sub yulan_Click()
    ' 处理预览列表项的选择
End Sub

' 字号勾选框点击事件
Private Sub zihao_Click()
    zihaodaxiao.Enabled = zihao.Value
    zihaodaxiaoanjian.Enabled = zihao.Value
End Sub

' 字体勾选框点击事件
Private Sub ziti_Click()
    zitisousuo.Enabled = ziti.Value
End Sub

' 字体列表变化事件
Private Sub zitisousuo_Change()
    ' 处理字体选择变化
End Sub

' 字体下拉框下拉事件 - 删除按需加载逻辑
Private Sub zitisousuo_DropButtonClick()
    ' 不再需要按需加载，字体列表在初始化时已全部加载
End Sub

' 字号大小输入框变化事件
Private Sub zihaodaxiao_Change()
    ' 同步TextBox和SpinButton的值
    If IsNumeric(zihaodaxiao.Text) Then
        Dim newSize As Double
        newSize = CDbl(zihaodaxiao.Text)
        If newSize >= 1 And newSize <= 3000 Then
            zihaodaxiaoanjian.Value = newSize
        End If
    End If
End Sub

' 字号调整按钮鼠标按下事件
Private Sub zihaodaxiaoanjian_MouseDown(ByVal Button As Integer, ByVal Shift As Integer, ByVal X As Single, ByVal Y As Single)
    Dim currentSize As Double
    Dim increment As Double

    ' 获取当前字号
    If IsNumeric(zihaodaxiao.Text) Then
        currentSize = CDbl(zihaodaxiao.Text)
    Else
        currentSize = 24 ' 默认字号
    End If

    ' 根据鼠标按键确定增减量
    If Button = 1 Then ' 左键点击
        increment = 1
    ElseIf Button = 2 Then ' 右键点击
        increment = 10
    Else
        increment = 1 ' 默认增减量
    End If

    ' 判断点击位置确定增减方向
    ' 如果点击在上半部分，增加；下半部分，减少
    If Y < zihaodaxiaoanjian.Height / 2 Then
        currentSize = currentSize + increment
    Else
        currentSize = currentSize - increment
    End If

    ' 限制字号范围
    If currentSize < 1 Then currentSize = 1
    If currentSize > 3000 Then currentSize = 3000

    ' 更新字号值
    zihaodaxiao.Text = CStr(currentSize)
End Sub

' 字号调整按钮变化事件（保留原有功能）
Private Sub zihaodaxiaoanjian_Change()
    ' 同步SpinButton和TextBox的值
    If IsNumeric(zihaodaxiaoanjian.Value) Then
        zihaodaxiao.Text = CStr(zihaodaxiaoanjian.Value)
    End If
End Sub

' 粗体勾选框点击事件
Private Sub Bold_Click()
    ' 处理粗体格式设置
End Sub

' 斜体勾选框点击事件
Private Sub xieti_Click()
    ' 处理斜体格式设置
End Sub

' 上标勾选框点击事件
Private Sub shangbiao_Click()
    Debug.Print "上标勾选框点击事件触发，当前值: " & shangbiao.Value
    ' 上标和下标互斥，如果选择上标则取消下标
    If shangbiao.Value = True Then
        Debug.Print "设置上标为True，取消下标"
        xiabiao.Value = False
    End If
    Debug.Print "上标勾选框点击事件结束，上标值: " & shangbiao.Value & ", 下标值: " & xiabiao.Value
End Sub

' 下标勾选框点击事件
Private Sub xiabiao_Click()
    Debug.Print "下标勾选框点击事件触发，当前值: " & xiabiao.Value
    ' 下标和上标互斥，如果选择下标则取消上标
    If xiabiao.Value = True Then
        Debug.Print "设置下标为True，取消上标"
        shangbiao.Value = False
    End If
    Debug.Print "下标勾选框点击事件结束，上标值: " & shangbiao.Value & ", 下标值: " & xiabiao.Value
End Sub

' 下划线勾选框点击事件
Private Sub xiahuaxian_Click()
    ' 处理下划线格式设置
End Sub

' 替换颜色勾选框点击事件
Private Sub tihuanyanse_Click()
    Color.Enabled = tihuanyanse.Value
End Sub

' 获取要查找的形状范围
Private Function myFindShapes() As ShapeRange
    Select Case fanwen.Text
        Case "当前文档"
            Dim p As Page
            Dim startPage As Page
            Set startPage = ActivePage
            Dim sr As New ShapeRange
            For Each p In ActiveDocument.Pages
                p.Activate
                sr.AddRange ActivePage.FindShapes(, cdrTextShape)
            Next
            startPage.Activate
            Set myFindShapes = sr
        Case "当前页面"
            Set myFindShapes = ActivePage.FindShapes(, cdrTextShape)
        Case "当前选择范围"
            Set myFindShapes = ActiveSelectionRange
    End Select
End Function

' 文本替换核心函数
Private Function myReplaceText(sr As ShapeRange, sFind As String, sRep As String, Optional findOnly As Boolean = False) As Long
    Dim reg As Object
    Dim replaceCount As Long

    replaceCount = 0

    ' 调试信息
    Debug.Print "=== myReplaceText 开始 ==="
    Debug.Print "查找文本: " & sFind
    Debug.Print "替换文本: " & sRep
    Debug.Print "仅查找模式: " & findOnly

    If sr Is Nothing Or sr.Count < 1 Then
        Debug.Print "错误: 没有选择的形状范围"
        myReplaceText = 0
        Exit Function
    End If

    If Trim(sFind) = "" Then
        Debug.Print "错误: 查找文本为空"
        myReplaceText = 0
        Exit Function
    End If

    ' 如果是替换模式，开始命令组以便用户可以一次性撤销
    If Not findOnly Then
        ActiveDocument.BeginCommandGroup "正则文本替换"
    End If

    ' 添加错误处理，确保命令组能正确结束
    On Error GoTo ErrorHandler

    ' 创建正则表达式对象
    Set reg = CreateObject("VBScript.RegExp")
    reg.Pattern = sFind
    reg.IgnoreCase = True
    reg.MultiLine = True
    reg.Global = True

    Debug.Print "形状范围数量: " & sr.Count

    Dim sh As Shape

    ' 遍历所有文本形状进行查找和替换
    For Each sh In sr
        If sh.Type = cdrTextShape Then
            Debug.Print "处理文本形状: " & sh.Name

            ' 设置当前正在处理的Shape
            Set currentProcessingShape = sh

            Dim txt As TextRange, matches As Object, i As Long, fi As Long
            Set txt = sh.Text.Story

            Debug.Print "文本内容: " & txt.Text

            Set matches = reg.Execute(txt.Text)
            Debug.Print "找到匹配数量: " & matches.Count

            For i = matches.Count - 1 To 0 Step -1
                With matches(i)
                    Debug.Print "处理匹配 " & i & ": 位置=" & .FirstIndex & ", 长度=" & .length & ", 内容=" & .Value

                    If .FirstIndex = 0 Then fi = 0 Else fi = .FirstIndex
                    Dim tr As TextRange
                    Set tr = txt.Range(fi, fi + .length)

                    ' 查找模式：只统计匹配项，不做任何修改
                    If findOnly Then
                        replaceCount = replaceCount + 1
                        Debug.Print "查找计数: " & replaceCount
                    Else
                        ' 替换模式：先执行文本替换，再应用格式
                        Debug.Print "执行文本替换: " & tr.Text & " -> " & sRep
                        tr.Text = sRep

                        ' 只有在替换模式下才应用格式，并且需要检查勾选框状态
                        Debug.Print "检查格式替换选项..."
                        ApplyChangeFormatIfEnabled tr

                        replaceCount = replaceCount + 1
                        Debug.Print "替换计数: " & replaceCount
                    End If

                    Set tr = Nothing
                End With
            Next i
        Else
            Debug.Print "跳过非文本形状: " & sh.Type
        End If
    Next sh

    Debug.Print "=== myReplaceText 结束，总计数: " & replaceCount & " ==="

    ' 正常结束，关闭错误处理
    On Error GoTo 0

    ' 如果是替换模式，结束命令组
    If Not findOnly Then
        ActiveDocument.EndCommandGroup
    End If

    myReplaceText = replaceCount
    Exit Function

ErrorHandler:
    ' 错误处理：确保命令组能正确结束
    Debug.Print "myReplaceText 发生错误: " & Err.Description

    ' 如果是替换模式，结束命令组
    If Not findOnly Then
        ActiveDocument.EndCommandGroup
    End If

    ' 返回已处理的计数
    myReplaceText = replaceCount
End Function

' 注意：myCheckFindFormat函数已被移除
' 查找过程不再检查格式条件，只进行文本匹配
' 格式替换选项仅在替换时生效

' 检查勾选框状态并应用替换格式
Private Sub ApplyChangeFormatIfEnabled(tr As TextRange)
    Debug.Print "--- ApplyChangeFormatIfEnabled 开始 ---"
    Debug.Print "当前文本: " & tr.Text
    Debug.Print "上标勾选框状态: " & shangbiao.Value
    Debug.Print "下标勾选框状态: " & xiabiao.Value

    ' 检查字体勾选框并应用字体
    If ziti.Value = True And Trim(zitisousuo.Text) <> "" Then
        Debug.Print "应用字体: " & zitisousuo.Text
        On Error Resume Next
        tr.Font = zitisousuo.Text
        If Err.Number <> 0 Then
            Debug.Print "字体设置错误: " & Err.Description
            Err.Clear
        Else
            Debug.Print "字体应用成功"
        End If
        On Error GoTo 0
    Else
        Debug.Print "跳过字体设置"
    End If

    ' 检查字号勾选框并应用字号
    If zihao.Value = True And Trim(zihaodaxiao.Text) <> "" Then
        Debug.Print "应用字号: " & zihaodaxiao.Text
        On Error Resume Next
        tr.Size = CDbl(zihaodaxiao.Text)
        If Err.Number <> 0 Then
            Debug.Print "字号设置错误: " & Err.Description
            Err.Clear
        Else
            Debug.Print "字号应用成功: " & tr.Size
        End If
        On Error GoTo 0
    Else
        Debug.Print "跳过字号设置"
    End If

    ' 检查颜色勾选框并应用颜色
    If tihuanyanse.Value = True Then
        Debug.Print "应用颜色，检查用户颜色选择状态"
        On Error Resume Next

        ' 根据用户是否主动选择颜色来决定处理方式
        If userSelectedColor And Not TextColor Is Nothing Then
            Debug.Print "用户已选择颜色，颜色类型: " & TextColor.Type & ", 颜色名称: " & TextColor.Name
            Debug.Print "保持用户选择的颜色模式，直接应用"
            ' 直接应用用户选择的颜色，保持原有颜色模式（RGB、CMYK等）
            tr.Fill.UniformColor.CopyAssign TextColor
        Else
            Debug.Print "用户未选择颜色，使用默认CMYK颜色(0,100,100,0)"
            ' 用户没有选择颜色，使用默认CMYK颜色
            Dim defaultColor As New Color
            defaultColor.CMYKAssign 0, 100, 100, 0
            tr.Fill.UniformColor.CopyAssign defaultColor
        End If

        If Err.Number <> 0 Then
            Debug.Print "颜色设置错误: " & Err.Description
            Err.Clear
        Else
            Debug.Print "颜色应用成功"
        End If

        On Error GoTo 0
    Else
        Debug.Print "跳过颜色设置"
    End If

    ' 检查粗体勾选框并应用粗体
    If Bold.Value = True Then
        Debug.Print "应用粗体: True"
        On Error Resume Next
        tr.Bold = True
        If Err.Number <> 0 Then
            Debug.Print "粗体设置错误: " & Err.Description
            Err.Clear
        Else
            Debug.Print "粗体应用成功: " & tr.Bold
        End If
        On Error GoTo 0
    Else
        Debug.Print "跳过粗体设置"
    End If

    ' 检查斜体勾选框并应用斜体
    If xieti.Value = True Then
        Debug.Print "应用斜体: True"
        On Error Resume Next
        tr.Italic = True
        If Err.Number <> 0 Then
            Debug.Print "斜体设置错误: " & Err.Description
            Err.Clear
        Else
            Debug.Print "斜体应用成功: " & tr.Italic
        End If
        On Error GoTo 0
    Else
        Debug.Print "跳过斜体设置"
    End If

    ' 检查下划线勾选框并应用下划线
    If xiahuaxian.Value = True Then
        Debug.Print "应用下划线: True"
        On Error Resume Next
        tr.Underline = cdrSingleThinFontLine
        If Err.Number <> 0 Then
            Debug.Print "下划线设置错误: " & Err.Description
            Err.Clear
        Else
            Debug.Print "下划线应用成功: " & tr.Underline
        End If
        On Error GoTo 0
    Else
        Debug.Print "跳过下划线设置"
    End If

    ' 检查上标勾选框并应用上标
    If shangbiao.Value = True Then
        Debug.Print "应用上标: True"
        On Error Resume Next

        ' 确保文本范围有效
        If tr.Characters.Count > 0 Then
            ' 使用FontPropertiesInRange方法应用上标
            tr.FontPropertiesInRange(1, tr.Characters.Count).Position = cdrSuperscriptFontPosition

            ' 如果当前处理的Shape不为空，也尝试直接在Shape上应用
            If Not currentProcessingShape Is Nothing Then
                If tr.Start > 0 And tr.length > 0 Then
                    currentProcessingShape.Text.FontPropertiesInRange(tr.Start, tr.length).Position = cdrSuperscriptFontPosition
                End If
            End If

            If Err.Number <> 0 Then
                Debug.Print "上标设置错误: " & Err.Description
                Err.Clear
            Else
                Debug.Print "上标应用成功"
            End If
        Else
            Debug.Print "上标应用失败：文本范围为空"
        End If

        On Error GoTo 0
    Else
        Debug.Print "跳过上标设置"
    End If

    ' 检查下标勾选框并应用下标
    If xiabiao.Value = True Then
        Debug.Print "应用下标: True"
        On Error Resume Next

        ' 确保文本范围有效
        If tr.Characters.Count > 0 Then
            ' 使用FontPropertiesInRange方法应用下标
            tr.FontPropertiesInRange(1, tr.Characters.Count).Position = cdrSubscriptFontPosition

            ' 如果当前处理的Shape不为空，也尝试直接在Shape上应用
            If Not currentProcessingShape Is Nothing Then
                If tr.Start > 0 And tr.length > 0 Then
                    currentProcessingShape.Text.FontPropertiesInRange(tr.Start, tr.length).Position = cdrSubscriptFontPosition
                End If
            End If

            If Err.Number <> 0 Then
                Debug.Print "下标设置错误: " & Err.Description
                Err.Clear
            Else
                Debug.Print "下标应用成功"
            End If
        Else
            Debug.Print "下标应用失败：文本范围为空"
        End If

        On Error GoTo 0
    Else
        Debug.Print "跳过下标设置"
    End If

    ' 检查取消设置勾选框并取消对应格式
    If quxiao.Value = True Then
        Debug.Print "执行取消设置操作"
        On Error Resume Next

        ' 如果勾选了粗体，取消粗体格式
        If Bold.Value = True Then
            Debug.Print "取消粗体格式"
            tr.Bold = False
        End If

        ' 如果勾选了斜体，取消斜体格式
        If xieti.Value = True Then
            Debug.Print "取消斜体格式"
            tr.Italic = False
        End If

        ' 如果勾选了上标，取消上标格式
        If shangbiao.Value = True Then
            Debug.Print "取消上标格式"
            If tr.Characters.Count > 0 Then
                tr.FontPropertiesInRange(1, tr.Characters.Count).Position = cdrNormalFontPosition
            End If

            ' 如果当前处理的Shape不为空，也尝试直接在Shape上应用
            If Not currentProcessingShape Is Nothing Then
                If tr.Start > 0 And tr.length > 0 Then
                    currentProcessingShape.Text.FontPropertiesInRange(tr.Start, tr.length).Position = cdrNormalFontPosition
                End If
            End If
        End If

        ' 如果勾选了下标，取消下标格式
        If xiabiao.Value = True Then
            Debug.Print "取消下标格式"
            If tr.Characters.Count > 0 Then
                tr.FontPropertiesInRange(1, tr.Characters.Count).Position = cdrNormalFontPosition
            End If

            ' 如果当前处理的Shape不为空，也尝试直接在Shape上应用
            If Not currentProcessingShape Is Nothing Then
                If tr.Start > 0 And tr.length > 0 Then
                    currentProcessingShape.Text.FontPropertiesInRange(tr.Start, tr.length).Position = cdrNormalFontPosition
                End If
            End If
        End If

        ' 如果勾选了下划线，取消下划线格式
        If xiahuaxian.Value = True Then
            Debug.Print "取消下划线格式"
            tr.Underline = cdrNoFontLine
        End If

        If Err.Number <> 0 Then
            Debug.Print "取消设置错误: " & Err.Description
            Err.Clear
        Else
            Debug.Print "取消设置操作完成"
        End If
        On Error GoTo 0
    Else
        Debug.Print "跳过取消设置"
    End If

    Debug.Print "--- ApplyChangeFormatIfEnabled 结束 ---"
End Sub

' 更新预览列表
Private Sub UpdatePreview()
    If chazhaowenben.Text = "" Then Exit Sub

    yulan.Clear

    Dim sr As ShapeRange, reg As Object
    Set sr = myFindShapes

    If sr.Count < 1 Then Exit Sub

    Set reg = CreateObject("VBScript.RegExp")
    reg.Pattern = chazhaowenben.Text
    reg.IgnoreCase = True
    reg.MultiLine = True
    reg.Global = True

    Dim sh As Shape, previewCount As Long
    previewCount = 0

    For Each sh In sr
        If sh.Type = cdrTextShape And previewCount < 50 Then ' 限制预览数量
            Dim txt As TextRange, matches As Object
            Set txt = sh.Text.Story
            Set matches = reg.Execute(txt)

            If matches.Count > 0 Then
                Dim previewText As String
                previewText = Left(txt.Text, 100) ' 显示前100个字符
                If Len(txt.Text) > 100 Then previewText = previewText & "..."
                yulan.AddItem previewText
                previewCount = previewCount + 1
            End If
        End If
    Next sh
End Sub